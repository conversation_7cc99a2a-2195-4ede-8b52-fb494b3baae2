from zhipuai import ZhipuAI

client = ZhipuAI(api_key="50f84348cb96970df4245c528d935715.eYf0lflS3gRK0X0N") # 请填写您自己的APIKey
response = client.chat.completions.create(
    model="GLM-3-Turbo",  # 填写需要调用的模型名称
    messages=[
        {
            "role": "user",
            "content": "请你作为童话故事大王，写一篇短篇童话故事，故事的主题是要永远保持一颗善良的心，要能够激发儿童的学习兴趣和想象力，同时也能够帮助儿童更好地理解和接受故事中所蕴含的道理和价值观。"
        }
    ],
)
print(response)