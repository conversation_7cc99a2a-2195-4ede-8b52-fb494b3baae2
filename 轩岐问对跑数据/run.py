import pandas as pd
import requests
import json

def format_json_content(content_str):
    """格式化JSON字符串，使其更易读"""
    try:
        # 尝试解析JSON字符串
        parsed_json = json.loads(content_str)
        # 格式化JSON，增加缩进和确保中文正确显示
        formatted_json = json.dumps(parsed_json, ensure_ascii=False, indent=2)
        return formatted_json
    except json.JSONDecodeError:
        # 如果解析失败，返回原始字符串
        return content_str
    except Exception as e:
        # 其他异常情况，返回错误信息和原始字符串
        return f"格式化失败: {str(e)}\n原始内容: {content_str}"

def call_api(type_num, base_info, syndrome=None, prescription=None):
    """调用接口的函数"""
    url = "https://ai-test.igancao.cn/gateway/xuanqi/chat/stream"
    
    payload = {
        "type": type_num,
        "base_info": base_info
    }
    
    if syndrome:
        payload["syndrome"] = syndrome
    if prescription:
        payload["prescription"] = prescription
        
    headers = {
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(url, headers=headers, json=payload, stream=True)
        response.raise_for_status()
        
        # 用于存储合并后的内容
        think_content = []
        text_content = []
        recall_list_content = []

        # 处理流式响应
        for line in response.iter_lines():
            if line:
                # 去除 "data: " 前缀
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    line = line[6:]
                    try:
                        data = json.loads(line)
                        if data['type'] == 'think':
                            think_content.append(data['content'])
                        elif data['type'] == 'text':
                            text_content.append(data['content'])
                        elif data['type'] == 'recall_list':
                            # 对recall_list类型的content进行JSON格式化
                            formatted_content = format_json_content(data['content'])
                            recall_list_content.append(formatted_content)
                    except json.JSONDecodeError:
                        continue

        # 合并内容
        think_result = ''.join(think_content)
        text_result = ''.join(text_content)
        recall_list_result = '\n---\n'.join(recall_list_content)  # 用分隔符连接多个recall_list

        return {
            'think': think_result,
            'text': text_result,
            'recall_list': recall_list_result
        }
        
    except requests.exceptions.RequestException as e:
        print(f"API调用出错: {e}")
        return None

def process_excel():
    try:
        # 读取Excel文件
        df = pd.read_excel('14.xlsx')
        
        # 创建结果列
        df['病历分析_思考'] = ''
        df['病历分析_结果'] = ''
        df['病历分析_召回列表'] = ''
        df['药方分析_思考'] = ''
        df['药方分析_结果'] = ''
        df['药方分析_召回列表'] = ''
        
        # 遍历每一行
        for index, row in df.iterrows():
            # 构造base_info
            base_info = f"""
- 主诉: {row['主诉']}
- 刻下症状: {row['刻下症状']}
- 体征: {row['体征']}
"""
            
            # 调用第一个接口(type=2)
            result1 = call_api(2, base_info, syndrome=row['六经证候'])
            if result1:
                df.at[index, '病历分析_思考'] = result1['think']
                df.at[index, '病历分析_结果'] = result1['text']
                df.at[index, '病历分析_召回列表'] = result1['recall_list']

            # 调用第二个接口(type=3)
            result2 = call_api(3, base_info, prescription=row['处方'])
            if result2:
                df.at[index, '药方分析_思考'] = result2['think']
                df.at[index, '药方分析_结果'] = result2['text']
                df.at[index, '药方分析_召回列表'] = result2['recall_list']
            
            print(f"处理完成第 {index+1} 行")
            
        # 保存结果到新的Excel文件
        output_file = '14_results.xlsx'
        df.to_excel(output_file, index=False)
        print(f"结果已保存到 {output_file}")
        
    except Exception as e:
        print(f"处理过程出错: {e}")

if __name__ == "__main__":
    process_excel()
