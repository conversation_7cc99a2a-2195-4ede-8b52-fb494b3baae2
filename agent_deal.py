# -*- coding: utf-8 -*-

import time
import ast
import orj<PERSON>
from openai import OpenAI
from openai.types.chat import ChatCompletionSystemMessageParam, ChatCompletionUserMessageParam

# client = OpenAI(api_key="sk-", base_url="http://10.0.11.94:9000/v1")
# client = OpenAI(api_key="sk-", base_url="http://10.0.9.54:9000/v1")
# client = OpenAI(api_key="sk-wfthjtvqdahnzaelbefbrvbahsbpicgeopwascxnwguvvwtd", base_url="https://api.siliconflow.cn/v1")
# client = OpenAI(api_key="59d8f45d143e4967b4f9d847a85f844f", base_url="https://wishub-x1.ctyun.cn/v1")
# client = OpenAI(api_key="sk-f61f373d45784ce5908a4bf2d7b4a800", base_url="https://api.deepseek.com/v1")
#client = OpenAI(api_key="sk-8O7LaJ7d4YP8JuPN16Fa330664924811A10aF4Ad1eFeA1Cc", base_url='https://one-api.igancao.cn/v1')
client = OpenAI(api_key="585362c1-ceab-420f-a955-f22d8fc57f7e", base_url='https://ark.cn-beijing.volces.com/api/v1')
# huoshan_llm_client = OpenAI(api_key='585362c1-ceab-420f-a955-f22d8fc57f7e', base_url='https://ark.cn-beijing.volces.com/api/v3')

# client = OpenAI(api_key="sk-AmsBjUwoqSqwyi6O83308dAb81384dF6A913E1C00d0323Fb", base_url="https://maas-api.cn-huabei-1.xf-yun.com/v1")

prompt = """
### 当前记录的病案：

{}

### 按要求处理成**json格式**，对应内容**必须为原文，不能杜撰**：
#### 诊次分割
    1. 将病案按就诊日期(visit_time)分割，每个日期对应一次就诊记录
    2. 日期格式化为标准的YYYY-MM-DD格式(如2025-01-01)
    3. 如果没有明确日期，必须置空，不要推测或杜撰

#### 患者基础信息
    1. 姓名(name)、年龄(age)、性别(gender)、住址(area)、电话(phone)
    2. 年龄若以月为单位，需转换为年，保留2位小数(如"6个月"转为"0.50岁")
    3. 信息不存在时必须置空

#### 患者主诉(desc)
    1. 患者用自身语言描述的主要症状或就诊原因
    2. 通常简短且包含症状部位、性质及持续时间(如"头痛3天")
    3. 直接提取原文，不要扩展或解释

#### 现病史(phi)
    1. 主诉的详细扩展，包含症状的完整演变过程
    2. 包括起病情况、诱因、症状特征、伴随症状、既往诊疗经过、加重/缓解因素
    3. 仅提取描述病情发展过程的内容，不包含当前症状表现
    4. 只允许针对主诉的治疗经过及程度变化进行记录，不允许记录除主诉外其它任何症状描述。

#### 复诊反馈(feedback)
    1. 仅提取患者对前次诊疗效果的明确反馈
    2. 应包含：前次治疗后症状变化、药物反应、检查结果反馈
    3. 不包含当前新出现的症状或体征
    4. 通常以"服药后..."、"治疗后..."、"上次用药后..."、"前服..."等开头
    5. 如果无法明确区分是反馈还是当前症状，则置空此项，将内容放入刻下症状

#### 既往史(pmh)
    - 提取患者又称过去病史，就医时医生向患者问询的患者既往的健康状况和过去曾经患过的疾病等方面的问题。
    - 通常提及某个症状持续多久，比如 停经多少年
    
#### 过敏史(allergies)
    - 提取患者对药物、食物或其他物质的过敏情况
    - 包括过敏物质名称及过敏反应类型

#### 查体(check_body)
    1. 医生进行体格检查发现的客观体征
    2. 包括生命体征、视诊、触诊、叩诊、听诊等结果
    3. 直接提取原文，不要解释或推测

#### 刻下症状(current_symptoms)
    1. 患者就诊当时的症状表现
    2. 通常以"刻下"、"现在"、"目前"等词语引导
    3. 包括当前所有症状，以及无法明确区分为复诊反馈的症状描述
    4. 如果病例中未明确区分复诊反馈和当前症状，则将相关内容全部放在此项

#### 病机(pathogenesis)
    1. 中医对疾病发生、发展机制的解释。不要放入成形的“病”或“证型”。
    2. 常见病机
        - 表束：表寒、中风、伤营、伤精
        - 太阴：伤学、血少、里寒、里虚、水饮
        - 阳明：里热、里燥、里结、水热、外热、外燥、外结

#### 辅助检查(aux_exam)
    1. 通过医学技术手段，为临床诊断、治疗决策或病情评估提供客观依据的检查方法。
    2. 通常包含：实验室检查、影像学检查、病理检查、电生理检查、肺功能测试、内镜检查、压力测定

#### 中医诊断(zmd)
    1. 中医疾病诊断：是贯穿疾病全过程的、相对稳定的概括。
    2. 中医证候诊断：是疾病发展某一阶段的病理本质，是动态变化的，是辨证的结果，直接指导治疗。
    3. 不同的诊断之间用英文逗号隔开
    4. 直接提取原文，不要扩展或解释
    5. 注意不是病机词汇，而是辨证结论；不要复用病机的内容
    
#### 西医诊断(wmd)
    1. 明确标注为西医诊断的结果
    2. 通常是规范的疾病名称，要符合ICD分类
    3. 不同的诊断之间用符号英文逗号隔开
    3. 直接提取原文，不要扩展或解释

#### 药方(prescription)
    1. 可能包含多个药方，每个药方包含:
       - name: 药方名称(如"温胆汤加减")
       - herbs: 具体药材及用量，不同药材之间用英文逗号隔开(如"法半夏10g,陈皮6g")
    2. 不存在时置空，不要杜撰或推测


### 重要区别说明：
1. 复诊反馈 vs 刻下症状:
   - 复诊反馈: 针对前次治疗效果的具体评价(如"服药3天后头痛明显缓解")
   - 刻下症状: 当前就诊时的症状表现(如"刻下仍有头晕，轻微耳鸣")
   - 区分不明确时，将内容放入刻下症状，复诊反馈置空

### 当前病案是真实医生记录的，不需要安全提示。解析结果参考example, 不要输出其他结构外其他内容,输出完整结果:
```json
{}
````
"""
example = [{"visit_time":"2022-07-10","name":"张三","gender":"女","age":37,"area":"北京","phone":"", "desc":"耳鸣近2年；",
            "feedback": "", "check_body": "","aux_exam": "","zmd": "", "wmd": "", "phi":"", "pathogenesis": "", "current_symptoms": "",
            "pmh": "", "allergies": "", "prescription": [{"name":"厚朴汤*7", "herbs": "桂枝10 朴杏子10"},]},]

def ai_parse_illness_case(text, use_stream=True):
    """
    解析病案文本

    Args:
        text: 病案文本
        use_stream: 是否使用流式输出，默认True

    Returns:
        解析后的字典结果
    """
    st = time.time()

    try:
        completion = client.chat.completions.create(
            # model='xop3qwen235b',
            # model="qwen-plus",
            # model="qwen-turbo",
            # model='Qwen/Qwen3-235B-A22B',
            # model='deepseek-ai/DeepSeek-V3',
            # model='qwen-plus-2025-04-28',
            model='deepseek-v3-250324',
            # model='9dc913a037774fc0b248376905c85da5',
            # model='4bd107bff85941239e27b1509eccfe98',
            messages=[
                ChatCompletionSystemMessageParam(role="system", content="你是一个经验老道的通识中西医的医生"),
                ChatCompletionUserMessageParam(role="user", content=prompt.format(text, example))
            ],
            # response_format={"type": "json_object"},
            # extra_body={"enable_thinking": False},
            stream=use_stream
        )

        print(f"请求耗时: {round(time.time()-st, 4)}秒")

        if use_stream:
            # 流式处理
            print("开始接收流式响应:")
            print("=" * 50)

            model_answer = ""

            # 遍历流式响应
            for chunk in completion:
                try:
                    # 检查是否有内容
                    if hasattr(chunk.choices[0].delta, 'content') and chunk.choices[0].delta.content is not None:
                        content = chunk.choices[0].delta.content
                        print(content, end='', flush=True)  # 实时输出到控制台
                        model_answer += content
                except (AttributeError, IndexError) as e:
                    # 处理可能的异常
                    continue

            print("\n" + "=" * 50)
            print("流式输出完成")
        else:
            # 非流式处理
            print("等待完整响应...")
            model_answer = completion.choices[0].message.content
            print("响应接收完成")

        # 处理完整的响应内容
        if "```" in model_answer:
            model_answer = model_answer.replace('```json', '').replace('```', '').strip()
            print('\n处理后的JSON内容:\n', model_answer)
            try:
                answer_dict = orjson.loads(model_answer)
            except:
                answer_dict = ast.literal_eval(model_answer)
            return answer_dict
        else:
            print('\n原始响应内容:\n', model_answer)
            try:
                answer_dict = orjson.loads(model_answer)
            except:
                answer_dict = ast.literal_eval(model_answer)
            return answer_dict

    except Exception as e:
        print(f"请求失败: {str(e)}")
        if "504" in str(e) or "timeout" in str(e).lower():
            print("建议：")
            print("1. 增加timeout参数值")
            print("2. 使用流式输出 use_stream=True")
            print("3. 检查网络连接")
        raise e


if __name__ == '__main__':
    text = """
丁燕忠     男     36     宁波      13957810815
2014-9-29
高血压10年，降压药在服，最高血压至170/110mmHg，现血压波动在140/90左右。痛风10年，左右无食指关节及右大足趾已有痛风石形成、偶发隐痛，尿酸600以上。下肢静脉曲张。
无恶风寒，恶热，不喜盖被，晨起未穿衣服时易背发凉、腹泻。汗可，口干明显，咽干，喜凉饮，无口苦，无胸闷气短，偶有头晕，纳佳，无反酸嗳气，时有肠鸣，无腹痛腹泻，大便稀、日1－2行，小便偏黄、稍频、夜尿2次。
脉沉细滑、左弦。舌紫，苔白腻滑、根厚。下睑红。扪手热，腹温。下肢（－）
今日血压130/100mmHg。
阳明里热；太阴水饮+里虚轻证
黄芩加半夏生姜汤
黄芩18，生白芍12，红枣24，炙甘草12，生姜9，法夏24   10剂
2015-3-27
跑步后左膝关节疼痛5日，激素及针灸治疗后缓解，查尿酸660umol/L，血压140/90mmHg。
现膝盖肿胀僵硬，无法弯屈，行走则疼痛，无灼热感。口干喜饮，咽干，汗可，不恶风寒，无头痛，偶头晕，5日前有发热，后自行热退，无胸闷，纳佳，腹无痛胀，时有肠鸣，无泛恶反酸烧心，大便日1－2行、成形，小便稍频、夜尿一行。
脉浮细弦数。舌红紫，苔薄黄水滑。下睑淡白边红。腹（－）。左膝关节扪之灼热，无红肿。下肢（－）
表阴+表束；阳明里热+外热；太阴血少+水饮
石膏96，知母36，粳米50，炒甘草12，桂枝18    7剂
火针：右手下焦（针后左膝明显减轻）
2015-4-3
左膝关节疼痛肿胀减轻，弯屈程度较前增加，口干喜饮咽干减轻，口臭，无胸晕，纳可，腹无痛胀，稍口苦，大便成形稍稀，无夜尿，无肠鸣，小便偏清、无频急涩痛，阴囊、肛门潮湿。
脉浮细滑。舌红紫，苔根黄厚腻。下睑淡白半红鲜。左膝关节灼热不明显。
今日尿酸593 umol/L，。ALT 71。
表束+表阴；阳明里热+外热+水热；太阴血少+外湿
麻黄连轺赤小豆汤
麻黄12，虎杖12，赤小豆48，杏仁12，桑白皮24，生姜12，红枣24     7剂
2015-4-10
左膝关节已无疼痛，弯屈疼痛已不明显、稍僵硬难以屈伸，晨起稍有口干，饮不多，无咽干口苦口臭，无胸闷气短，纳可，腹无痛胀，无泛恶，大便稍溏，肠鸣，小便稍有排尿不尽感，阴囊、肛门湿热明显减轻。无手足发凉。
脉浮滑，左弦，右细。舌紫红，苔根黄厚腻。下睑淡白边鲜红。腹大。左膝关节稍有灼热感。
尿酸684，ALT已正常。
阳明外热+里热轻证+水热；太阴水饮+血少轻证；表束+表阴
茵陈蒿汤
茵陈60，茯苓9，猪苓9，泽泻24，桂枝3，生白术12    7剂
2015-4-24
10日前行步4KM后左膝肿痛反复，难以屈伸，服止痛药及消炎药后稍缓，现左膝肿胀、难以屈伸，无疼痛灼热，右下肢原有一半手掌大小丘疹、搔痒，现已渐好。小便淡、偶色深，排尿不尽感，无频急涩痛，阴囊稍有潮湿，稍口干喜饮，汗可，无头晕痛，纳可，无泛恶，无手足凉麻。
脉细弦稍浮，左关紧。舌红紫，苔薄腻、根腻黄。下睑左淡白半红，右淡白边红。左膝稍肿胀，无灼热感。
太阴水饮+血少；阳明水热+里热轻证；表束+表阴
半量续命煮散
麻黄9，桂枝6，石膏18，生姜15，川芎6，炒甘草6，淡附片6，细辛6，茯苓6，生白术9，独活6，升麻9，防己9，防风6，杏仁9，党参2，北沙参2，生晒参2
2015-5-8
膝盖疼痛明显减轻，已能正常行走，屈伸向可，久坐偶有酸麻。小便色较黄，仍有排尿不尽感。晨起稍有口干，白日饮不多。阴囊潮湿。大便调、日2行。纳可。汗可，食汗出，无盗汗。足底时有细小水泡、搔痒。
脉浮细弦稍滑。舌偏红，苔薄润。下睑淡白边红。
桂枝加黄芪汤
桂枝18，生白芍18，生姜18，红枣24，炒甘草12，黄芪30    9剂
2015-5-17
左膝盖剧烈弯屈时微有酸胀感，微似有肿胀感、不明显，无麻木。小便偏黄，仍有排尿不尽感，无频急夜尿，晨起稍有口干，肠鸣，阴囊潮湿减轻、仍有，足底稍有细小水泡、搔痒。纳可，汗可，恶热，夜间不喜盖被。大便调。
脉细弦滑，左稍浮，右偏沉。舌紫红，胖大，质厚，苔根黄腻。下睑左偏红、中心淡白，右淡白边红。扪手潮、温热。左膝局限性肿胀。下肢微有按肿。
芪芍桂酒汤（备用：小续命汤）
黄芪30，桂枝18，生白芍18，陈醋30ML   14剂
2015-5-31
10日前无明显诱因下左足底疼痛，右踝疼痛肿胀，服秋水仙碱后减轻，现已不明显。近两日右足底稍有疼痛，受凉后夜间腹泻2次，时有头皮发麻、头晕、近日尚可，无明显口干，晨起稍有口干欲饮，无口苦，咽中稍有咯痰，小便偏黄，排尿不尽感较前减轻，阴囊潮湿感，腋下易汗出，身汗尚可，天热时手足心稍有汗出，纳可，肠鸣，腹无痛胀，大便偏稀，无手足凉，左膝已无肿胀感、弯屈后微有不适。足底受潮则脱屑、稍有搔痒。不恶风寒，晨起后背稍恶寒、受凉则易腹泻。
脉弦滑细浮。舌红稍紫，苔滑、根黄腻稍厚。下睑淡白半红。腹满偏凉。下肢（－）。
表束+伤营轻证+表阴轻证；太阴水饮+血少轻证+里虚轻证+表寒轻证；阳明里热轻证+水热+外热
五苓散
泽泻24，茯苓12，生白术18，猪苓12，桂枝6      7剂
放血：侧三里、侧下三里附近皮肤浅表静脉刺血
针：中白、下白，手五金、手千金，脾三叉        
2015-6-7
左膝稍有酸痛、屈伸略有不利，右足底搔痒，大便调、日1－2行，无头晕、头皮发麻，晨起口干、白日尚可，稍有咯痰，小便淡黄、排尿不尽感已不明显、稍有，阴囊潮湿仍稍有，汗可不多、腋下仍汗出较多、手足微有汗出，肠鸣，纳可。
脉弦滑稍细浮，左寸沉。舌红紫，苔白腻滑。下睑淡边红有血丝。
芪芍桂酒汤
黄芪30，桂枝18，生白芍18，陈醋30ML   7剂
针：右三叉，左三其（横刺透手少阳经），州水
2015-6-14
左膝酸痛已不明显，屈伸略有不利，右足底搔痒减轻，大便调、日一行、成形略稀，晨起口干、咯痰，小便稍有排尿不尽感，阴囊潮湿仍有，腋下易汗出。肠鸣不明显。稍恶热，易汗出。
脉弦滑稍浮。舌红紫，苔薄白腻滑。下睑淡白半红。
芪芍桂酒汤
黄芪30，桂枝18，生白芍18，陈醋30ML   14剂
针：左三叉，右三其（横刺透手少阳经），右分金
2015-6-28
左膝盖仍稍有屈伸不利、略有肿胀，近日烦劳，足下搔痒脱屑、无渗液，晨起口干，恶热易汗出，纳可，腹无痛胀，无肠鸣，阴囊潮湿，小便仍有尿排不尽感，听见水流声时稍有尿急，大便较稀。
脉浮滑稍弦濡。舌红紫，苔薄白水滑略腻。下睑淡白半红。目下稍有浮肿。
麻黄连轺赤小豆汤
麻黄12，虎杖12，赤小豆48，桑白皮24，杏仁18，生姜12，红枣24，炒甘草12      14剂
2015-7-12
小便偏黄，憋尿后小便稍有不畅，尿排不尽、阴囊潮湿，左膝盖仍稍屈伸不利、略有肿胀，疲乏，纳可，夜间及晨起口干，大便成形偏稀。
脉浮稍弦滑。舌红紫，胖大，苔根黄腻。下睑右半红半白，左稍红。
表束+表阴；太阴水饮+血少轻证；阳明水热+里热
麻黄连轺赤小豆汤
麻黄12，虎杖12，赤小豆48，桑白皮24，杏仁18，生姜12，红枣24，炒甘草12      14剂
2015-7-26
近日食欲欠佳，不知饥，食量尚可。易疲乏，下肢无力沉重。小便不畅、不尽感稍有减轻、仍有，阴囊潮湿，汗易出，左膝稍有活动不利，无名指关节时有疼痛、胀肿感、屈伸受限。稍有口干，无口苦，饮可不多。无心悸，无泛恶。时有头晕。
脉左弦细滑稍紧，右细稍浮滑。舌红稍深，苔薄、根黄腻。下睑左淡白半红，右淡白边红鲜。右眼泡微有浮肿。
乐令黄芪汤
黄芪12，党参4，北沙参4，生晒参4，陈皮12，当归12，桂枝12，生白芍12，细辛12，前胡12，炒甘草12，茯苓12，麦冬24，生姜30，法夏12，红枣30    14剂
2015-8-9
小便已基本正常，微有不畅感、不明显，阴囊潮湿，大便日一行、质软稍溏，左膝盖仍稍有活动不利、肿胀，手指关节肿胀感、已无疼痛，两拇指关节较前缩小，纳可，晨起稍有口干、较前减轻，右足外侧偶有疼痛，无头晕。汗多易出、夜寐易有，汗后不凉。
脉细滑稍弦，左浮，右略沉。舌红紫，苔白腻稍黄水滑。有口气。下睑淡白半红。
表束+表阴+中风轻证；太阴水饮+血少轻证+外湿；阳明里热+水热轻证
半量续命煮散
麻黄9，桂枝6，石膏18，生姜15，川芎6，炒甘草6，淡附片6，细辛6，茯苓6，生白术9，独活6，升麻9，防己9，防风6，杏仁9，党参2，北沙参2，生晒参2　　7剂
2015-8-16
左足底稍有疼痛，双足搔痒起水泡，无口干苦，左膝屈伸稍有不利，小便顺畅，阴囊潮湿，大便日一行、偏稀。汗可较多。易困倦。手指肿胀感。
脉弦细滑浮。舌紫红，苔根腻。下睑淡白边红。
前胡建中汤
前胡12，桂枝12，生白芍12，黄芪12，当归12，茯苓12，党参3，北沙参3，生晒参3，甘草6，生姜36，法夏12，白蜜40　　　14剂
2015/8/30
左膝弯曲时疼痛减轻、仍稍有，肿胀已消，足趾不痛，小便调、无不利频急，阴囊潮湿，大便成形稍稀，汗多，无盗汗，晨起口干喜饮，白日尚可，足底起水泡、略有搔痒，手指肿胀感、按痛减轻、弯曲不利，纳可。无头晕头痛。
脉浮滑稍弦细。舌红，苔水滑、根腻。下睑淡白边红有血丝。
BP 140/90mmHg。现服缬沙坦氨氯地平片，1#QD。
太阴血少+外湿；阳明里热轻证+水热轻证；表束+表阴轻证
前胡建中汤
前胡12，桂枝12，生白芍18，黄芪12，当归12，茯苓12，党参4，北沙参4，生晒参4，甘草6，生姜30，旱半夏12，饴糖40　　　14剂
心膝，灵骨，大白，中白，尺泽，三阴交，阴陵   
2015/9/13
左膝盖弯曲则酸胀，易僵硬，疼痛不明显，足底水泡已消，晨起稍有口干，饮可，无口苦，两无名指关节肿胀感、按之稍有疼痛，大便调、日一行、成形偏稀，小便清、无不尽感，阴囊、腋下潮湿，活动后易汗出，减半片降压药时头晕，
脉左浮弦滑，右稍弦略浮。舌红胖大，苔水润。下睑淡白边红。
表束+表阴轻证；阳明里热轻证+水热轻证；太阴水饮+血少轻证
芪芍桂酒汤
黄芪30，桂枝18，生白芍18，苦酒45ML     14剂
2015/9/27
近两周4-5夜有夜尿，小便淡、稍有排尿不尽感，阴囊潮湿，大便成形偏稀，左膝酸、易僵硬，口干，饮偏多，汗多，稍动则汗出，恶热，手指关节肿胀，无头晕，嗜睡，肠鸣减轻。
脉浮弦滑。舌紫，胖大，苔薄、根白腻。下睑淡白边红。
表束+表阴；太阴水饮+血少；阳明里热+外热
芪芍桂酒汤
黄芪30，桂枝18，生白芍18，苦酒45ML     14剂
2015/10/18
小便略频、尿不尽感，阴囊潮湿，夜尿一行，困倦，手心易汗出，恶热，大便成形、日一行，左膝发紧硬僵感、弯曲时疼痛，稍有口干咽干，饮不多，无口苦。晨起咽中少量咯痰，色青。手指关节肿胀，纳可，肠鸣不明显，腹无痛胀。喜忘。无胸闷气短，无心悸。持物时双手颤震。无腰酸背痛。
脉细弦浮滑，左寸略沉。舌红，胖大，苔薄水滑。下睑淡白边红。扪手温。腹温、满大、软。左膝扪稍凉，下肢无按肿甲错，右下肢浅表静脉曲张。
表束+表阴；太阴水饮+血少；阳明里热+外热
半量续命煮散
麻黄9，桂枝6，石膏18，生姜15，川芎6，炒甘草6，白附片6，细辛6，茯苓6，生白术9，独活6，升麻9，防己9，防风6，杏仁9，党参2，北沙参2，生晒参2　　7剂
针：心膝，肩中，李白，云白，双三阴交    左膝刺血

"""

    print("选择运行模式:")
    print("1. 流式输出 (推荐，可以实时看到生成过程)")
    print("2. 非流式输出 (等待完整结果)")

    choice = input("请输入选择 (1/2，默认为1): ").strip()
    result = ai_parse_illness_case(text, use_stream=False)
    # if choice == "2":
    #     print("\n=== 使用非流式模式 ===")
    #     result = ai_parse_illness_case(text, use_stream=False)
    # else:
    #     print("\n=== 使用流式模式 ===")
    #     result = ai_parse_illness_case(text, use_stream=True)

    print(f"\n最终解析结果类型: {type(result)}")
    print(f"解析结果长度: {len(result) if isinstance(result, (list, dict)) else 'N/A'}")

