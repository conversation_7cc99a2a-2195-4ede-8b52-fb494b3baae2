# Copyright 2014 Cloudera, Inc. or its affiliates. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License"); you
# may not use this file except in compliance with the License. You
# may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied. See the License for the specific language governing
# permissions and limitations under the License. See accompanying
# LICENSE file.
#
# Sample Accumulo configuration properties
#
# You may either set properties here or via the command line.
#

# This will influence the keys we write
accumulo.columnFamily=YCSB

# This should be set based on your Accumulo cluster
#accumulo.instanceName=ExampleInstance

# Comma separated list of host:port tuples for the ZooKeeper quorum used
# by your Accumulo cluster
#accumulo.zooKeepers=zoo1.example.com:2181,zoo2.example.com:2181,zoo3.example.com:2181

# This user will need permissions on the table YCSB works against
#accumulo.username=ycsb
#accumulo.password=protectyaneck

# Controls how long our client writer will wait to buffer more data
# measured in milliseconds
accumulo.batchWriterMaxLatency=30000

# Controls how much data our client will attempt to buffer before sending
# measured in bytes
accumulo.batchWriterSize=100000

# Controls how many worker threads our client will use to parallelize writes
accumulo.batchWriterThreads=1
